# 服务端唤醒提示音功能测试

## 功能描述
在收到 TTS start 消息时，如果 `is_server_side_wakeup = true`，播放叮咚提示音。

## 实现逻辑

### 1. 服务端唤醒标志设置
当收到以下消息时，`is_server_side_wakeup` 被设置为 `true`：
- `{"type": "wakeup"}` 消息
- `{"type": "player"}` 消息

### 2. TTS start 消息处理
当收到 `{"type": "tts", "state": "start"}` 消息时：
- 检查 `is_server_side_wakeup` 是否为 `true`
- 如果是，播放 `Lang::Sounds::OGG_POPUP` 提示音（叮咚声）
- 播放后将 `is_server_side_wakeup` 重置为 `false`

### 3. 代码修改位置
文件：`main/application.cc`
行数：485-501

```cpp
if (strcmp(state->valuestring, "start") == 0) {
    Schedule([this]() {
        aborted_ = false;
        
        // 如果是服务端唤醒，播放叮咚提示音
        if (is_server_side_wakeup) {
            audio_service_.PlaySound(Lang::Sounds::OGG_POPUP);
            is_server_side_wakeup = false; // 重置标志位
        }
        
        if (device_state_ == kDeviceStateIdle || device_state_ == kDeviceStateListening) {
            SetDeviceState(kDeviceStateSpeaking);
        }
    });
}
```

## 测试场景

### 场景1：服务端唤醒后TTS
1. 发送 `{"type": "wakeup"}` 消息
2. 发送 `{"type": "tts", "state": "start"}` 消息
3. **预期结果**：播放叮咚提示音

### 场景2：播放器唤醒后TTS
1. 发送 `{"type": "player"}` 消息
2. 发送 `{"type": "tts", "state": "start"}` 消息
3. **预期结果**：播放叮咚提示音

### 场景3：本地唤醒后TTS
1. 本地唤醒词触发
2. 发送 `{"type": "tts", "state": "start"}` 消息
3. **预期结果**：不播放叮咚提示音（因为 `is_server_side_wakeup` 为 `false`）

### 场景4：重复TTS消息
1. 发送 `{"type": "wakeup"}` 消息
2. 发送 `{"type": "tts", "state": "start"}` 消息（第一次）
3. 发送 `{"type": "tts", "state": "start"}` 消息（第二次）
4. **预期结果**：只有第一次播放叮咚提示音，第二次不播放

## 相关音频文件
- 提示音文件：`Lang::Sounds::OGG_POPUP`
- 音频文件位置：`main/assets/common/popup.ogg`
- 其他可用音频：
  - `OGG_SUCCESS`：成功音
  - `OGG_EXCLAMATION`：警告音
  - `OGG_VIBRATION`：震动音

## 注意事项
1. 提示音播放是异步的，不会阻塞TTS播放
2. `is_server_side_wakeup` 标志在播放提示音后立即重置，确保只播放一次
3. 该功能只在服务端唤醒场景下生效，不影响本地唤醒的正常流程
