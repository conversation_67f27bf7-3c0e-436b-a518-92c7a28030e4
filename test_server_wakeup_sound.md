# 服务端唤醒提示音功能测试

## 功能描述
在收到 TTS start 消息时，如果 `is_server_side_wakeup = true`，播放叮咚提示音，并确保提示音播放完成后继续播放服务端推送的音频流。

## 实现逻辑

### 1. 服务端唤醒标志设置
当收到以下消息时，`is_server_side_wakeup` 被设置为 `true`：
- `{"type": "wakeup"}` 消息
- `{"type": "player"}` 消息

### 2. TTS start 消息处理
当收到 `{"type": "tts", "state": "start"}` 消息时：
- 先设置设备状态为 `kDeviceStateSpeaking`
- 检查 `is_server_side_wakeup` 是否为 `true`
- 如果是，播放 `Lang::Sounds::OGG_AFTER_SPEAK` 提示音
- 播放后将 `is_server_side_wakeup` 重置为 `false`

### 3. TTS stop 消息处理优化
当收到 `{"type": "tts", "state": "stop"}` 消息时：
- 创建异步线程等待音频播放完成
- 使用 `audio_service_.IsIdle()` 检查音频队列状态
- 最多等待3秒，确保提示音和TTS音频都播放完成
- 然后切换到相应的设备状态

### 4. 代码修改位置
文件：`main/application.cc`

#### TTS start 处理（行数：490-501）
```cpp
Schedule([this]() {
    aborted_ = false;
    if (device_state_ == kDeviceStateIdle || device_state_ == kDeviceStateListening) {
        SetDeviceState(kDeviceStateSpeaking);

        // 如果是服务端唤醒，播放叮咚提示音
        if (is_server_side_wakeup) {
            audio_service_.PlaySound(Lang::Sounds::OGG_AFTER_SPEAK);
            is_server_side_wakeup = false; // 重置标志位
        }
    }
});
```

#### TTS stop 处理（行数：503-526）
```cpp
Schedule([this]() {
    if (device_state_ == kDeviceStateSpeaking) {
        // 创建一个异步任务来等待音频播放完成
        std::thread([this]() {
            // 等待音频播放完成，避免提示音被中断
            int wait_count = 0;
            while (!audio_service_.IsIdle() && wait_count < 30) { // 最多等待3秒
                vTaskDelay(pdMS_TO_TICKS(100));
                wait_count++;
            }

            // 使用Schedule确保状态切换在主线程中执行
            Schedule([this]() {
                if (device_state_ == kDeviceStateSpeaking) {
                    if (listening_mode_ == kListeningModeManualStop) {
                        SetDeviceState(kDeviceStateIdle);
                    } else {
                        SetDeviceState(kDeviceStateListening);
                    }
                }
            });
        }).detach();
    }
});
```

## 测试场景

### 场景1：服务端唤醒后TTS（完整流程）
1. 发送 `{"type": "wakeup"}` 消息
2. 发送 `{"type": "tts", "state": "start"}` 消息
3. 服务端推送TTS音频流
4. 发送 `{"type": "tts", "state": "stop"}` 消息
5. **预期结果**：
   - 播放叮咚提示音
   - 提示音播放完成后继续播放TTS音频
   - TTS播放完成后正确切换到listening状态

### 场景2：播放器唤醒后TTS
1. 发送 `{"type": "player"}` 消息
2. 发送 `{"type": "tts", "state": "start"}` 消息
3. 服务端推送TTS音频流
4. 发送 `{"type": "tts", "state": "stop"}` 消息
5. **预期结果**：同场景1

### 场景3：本地唤醒后TTS
1. 本地唤醒词触发
2. 发送 `{"type": "tts", "state": "start"}` 消息
3. 服务端推送TTS音频流
4. 发送 `{"type": "tts", "state": "stop"}` 消息
5. **预期结果**：
   - 不播放叮咚提示音
   - 直接播放TTS音频
   - TTS播放完成后正确切换状态

### 场景4：重复TTS消息
1. 发送 `{"type": "wakeup"}` 消息
2. 发送 `{"type": "tts", "state": "start"}` 消息（第一次）
3. 发送 `{"type": "tts", "state": "start"}` 消息（第二次）
4. **预期结果**：只有第一次播放叮咚提示音，第二次不播放

### 场景5：快速TTS停止
1. 发送 `{"type": "wakeup"}` 消息
2. 发送 `{"type": "tts", "state": "start"}` 消息
3. 立即发送 `{"type": "tts", "state": "stop"}` 消息（无音频流）
4. **预期结果**：
   - 播放叮咚提示音
   - 提示音播放完成后切换到listening状态

## 关键改进

### 1. 音频播放顺序保证
- 提示音和TTS音频流都通过同一个音频解码队列播放
- 提示音先入队，确保优先播放

### 2. 状态切换优化
- 使用异步线程等待音频播放完成
- 避免阻塞主事件循环
- 确保状态切换在主线程中执行

### 3. 超时保护
- 最多等待3秒音频播放完成
- 防止因音频问题导致状态卡死

## 相关音频文件
- 提示音文件：`Lang::Sounds::OGG_AFTER_SPEAK`
- 音频文件位置：根据语言包配置
- 其他可用音频：
  - `OGG_POPUP`：弹出音
  - `OGG_SUCCESS`：成功音
  - `OGG_EXCLAMATION`：警告音
  - `OGG_VIBRATION`：震动音

## 注意事项
1. 提示音和TTS音频流使用同一个播放队列，确保播放顺序
2. `is_server_side_wakeup` 标志在播放提示音后立即重置，确保只播放一次
3. 异步等待机制避免阻塞主事件循环
4. 超时保护确保系统稳定性
5. 该功能只在服务端唤醒场景下生效，不影响本地唤醒的正常流程
